import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/services/voice_translation/voice_recording_service.dart';

/// A widget that displays the audio level during recording
class AudioLevelIndicator extends ConsumerWidget {
  /// The number of bars to display
  final int barCount;
  
  /// The height of the widget
  final double height;
  
  /// The width of the widget
  final double width;
  
  /// The color of the active bars
  final Color activeColor;
  
  /// The color of the inactive bars
  final Color inactiveColor;
  
  /// The radius of the bar corners
  final double borderRadius;
  
  /// The spacing between bars
  final double spacing;
  
  /// The animation duration
  final Duration animationDuration;
  
  /// Creates a new audio level indicator
  const AudioLevelIndicator({
    super.key,
    this.barCount = 20,
    this.height = 60,
    this.width = double.infinity,
    this.activeColor = Colors.green,
    this.inactiveColor = Colors.grey,
    this.borderRadius = 3.0,
    this.spacing = 2.0,
    this.animationDuration = const Duration(milliseconds: 150),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to the amplitude stream from the voice recording service
    final voiceRecordingService = ref.watch(voiceRecordingServiceProvider);
    
    return StreamBuilder<double>(
      stream: voiceRecordingService.amplitudeStream,
      builder: (context, snapshot) {
        // Default to 0 if no data
        final level = snapshot.data ?? 0.0;
        
        return SizedBox(
          height: height,
          width: width,
          child: _AudioLevelBars(
            level: level,
            barCount: barCount,
            activeColor: activeColor,
            inactiveColor: inactiveColor,
            borderRadius: borderRadius,
            spacing: spacing,
            animationDuration: animationDuration,
          ),
        );
      },
    );
  }
}

/// The bars that display the audio level
class _AudioLevelBars extends StatelessWidget {
  /// The current audio level (0.0 to 1.0)
  final double level;
  
  /// The number of bars to display
  final int barCount;
  
  /// The color of the active bars
  final Color activeColor;
  
  /// The color of the inactive bars
  final Color inactiveColor;
  
  /// The radius of the bar corners
  final double borderRadius;
  
  /// The spacing between bars
  final double spacing;
  
  /// The animation duration
  final Duration animationDuration;
  
  /// Creates a new audio level bars widget
  const _AudioLevelBars({
    required this.level,
    required this.barCount,
    required this.activeColor,
    required this.inactiveColor,
    required this.borderRadius,
    required this.spacing,
    required this.animationDuration,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate the number of active bars
    final activeBarCount = (level * barCount).round();
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(barCount, (index) {
        // Determine if this bar should be active
        final isActive = index < activeBarCount;
        
        // Calculate the height percentage based on the bar's position
        // Bars in the middle are taller than those at the edges
        final heightPercentage = _calculateHeightPercentage(index);
        
        return Expanded(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: spacing / 2),
            child: AnimatedContainer(
              duration: animationDuration,
              decoration: BoxDecoration(
                color: isActive ? activeColor : inactiveColor,
                borderRadius: BorderRadius.circular(borderRadius),
              ),
              height: heightPercentage * 100,
            ),
          ),
        );
      }),
    );
  }
  
  /// Calculate the height percentage for a bar at the given index
  double _calculateHeightPercentage(int index) {
    // Create a bell curve effect where bars in the middle are taller
    final middle = barCount / 2;
    final distance = (index - middle).abs();
    final maxDistance = middle;
    
    // Calculate height percentage (0.3 to 1.0)
    return 1.0 - (distance / maxDistance) * 0.7;
  }
}

/// A widget that displays a circular audio level indicator
class CircularAudioLevelIndicator extends ConsumerWidget {
  /// The size of the widget
  final double size;
  
  /// The color of the active circle
  final Color activeColor;
  
  /// The color of the inactive circle
  final Color inactiveColor;
  
  /// The animation duration
  final Duration animationDuration;
  
  /// The minimum size of the circle
  final double minSize;
  
  /// Creates a new circular audio level indicator
  const CircularAudioLevelIndicator({
    super.key,
    this.size = 80,
    this.activeColor = Colors.green,
    this.inactiveColor = Colors.grey,
    this.animationDuration = const Duration(milliseconds: 150),
    this.minSize = 0.3,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to the amplitude stream from the voice recording service
    final voiceRecordingService = ref.watch(voiceRecordingServiceProvider);
    
    return StreamBuilder<double>(
      stream: voiceRecordingService.amplitudeStream,
      builder: (context, snapshot) {
        // Default to 0 if no data
        final level = snapshot.data ?? 0.0;
        
        // Calculate the size of the inner circle
        final innerSize = size * (minSize + level * (1 - minSize));
        
        return SizedBox(
          height: size,
          width: size,
          child: Center(
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Outer circle (inactive)
                Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: inactiveColor,
                  ),
                ),
                // Inner circle (active)
                AnimatedContainer(
                  duration: animationDuration,
                  width: innerSize,
                  height: innerSize,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: activeColor,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// A widget that displays a waveform audio level indicator
class WaveformAudioLevelIndicator extends ConsumerWidget {
  /// The height of the widget
  final double height;
  
  /// The width of the widget
  final double width;
  
  /// The color of the waveform
  final Color color;
  
  /// The animation duration
  final Duration animationDuration;
  
  /// Creates a new waveform audio level indicator
  const WaveformAudioLevelIndicator({
    super.key,
    this.height = 60,
    this.width = double.infinity,
    this.color = Colors.blue,
    this.animationDuration = const Duration(milliseconds: 150),
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Listen to the amplitude stream from the voice recording service
    final voiceRecordingService = ref.watch(voiceRecordingServiceProvider);
    
    return StreamBuilder<double>(
      stream: voiceRecordingService.amplitudeStream,
      builder: (context, snapshot) {
        // Default to 0 if no data
        final level = snapshot.data ?? 0.0;
        
        return SizedBox(
          height: height,
          width: width,
          child: CustomPaint(
            painter: _WaveformPainter(
              level: level,
              color: color,
            ),
          ),
        );
      },
    );
  }
}

/// A custom painter that draws a waveform
class _WaveformPainter extends CustomPainter {
  /// The current audio level (0.0 to 1.0)
  final double level;
  
  /// The color of the waveform
  final Color color;
  
  /// Creates a new waveform painter
  const _WaveformPainter({
    required this.level,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    final path = Path();
    
    // Start at the left edge, middle height
    path.moveTo(0, size.height / 2);
    
    // Draw the waveform
    for (double x = 0; x < size.width; x++) {
      // Calculate the y position using a sine wave
      // The amplitude is determined by the audio level
      final amplitude = size.height / 3 * level;
      final y = size.height / 2 + sin(x / 10) * amplitude;
      
      path.lineTo(x, y);
    }
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(_WaveformPainter oldDelegate) {
    return oldDelegate.level != level || oldDelegate.color != color;
  }
}
