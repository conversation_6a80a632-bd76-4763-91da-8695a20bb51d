import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:culture_connect/models/travel/document/travel_documents.dart';
import 'package:culture_connect/theme/app_colors.dart';
import 'package:culture_connect/theme/app_text_styles.dart';
import 'package:culture_connect/widgets/common/custom_text_field.dart';
import 'package:culture_connect/widgets/common/date_picker_field.dart';

/// A form for uploading travel documents
class DocumentUploadForm extends StatefulWidget {
  /// The type of document to upload
  final TravelDocumentType documentType;
  
  /// The document to edit (null for new documents)
  final TravelDocument? document;
  
  /// Callback when the form is submitted
  final Function(TravelDocument document, List<File> images) onSubmit;
  
  /// Creates a new document upload form
  const DocumentUploadForm({
    Key? key,
    required this.documentType,
    this.document,
    required this.onSubmit,
  }) : super(key: key);

  @override
  State<DocumentUploadForm> createState() => _DocumentUploadFormState();
}

class _DocumentUploadFormState extends State<DocumentUploadForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _documentNumberController = TextEditingController();
  final _issuedByController = TextEditingController();
  DateTime? _issuedDate;
  DateTime? _expiryDate;
  final _notesController = TextEditingController();
  
  // Passport specific fields
  final _nationalityController = TextEditingController();
  final _countryCodeController = TextEditingController();
  final _placeOfBirthController = TextEditingController();
  DateTime? _dateOfBirth;
  String _gender = 'M';
  final _mrzLine1Controller = TextEditingController();
  final _mrzLine2Controller = TextEditingController();
  
  // Visa specific fields
  VisaType _visaType = VisaType.tourist;
  VisaEntryType _entryType = VisaEntryType.single;
  final _countryOfIssueController = TextEditingController();
  final _countryValidForController = TextEditingController();
  int _maxStayDuration = 90;
  final _numberOfEntriesController = TextEditingController();
  final _processingTimeController = TextEditingController();
  final _applicationReferenceController = TextEditingController();
  
  // Document images
  final List<File> _images = [];
  final _imagePicker = ImagePicker();
  
  @override
  void initState() {
    super.initState();
    _initializeForm();
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _documentNumberController.dispose();
    _issuedByController.dispose();
    _notesController.dispose();
    _nationalityController.dispose();
    _countryCodeController.dispose();
    _placeOfBirthController.dispose();
    _mrzLine1Controller.dispose();
    _mrzLine2Controller.dispose();
    _countryOfIssueController.dispose();
    _countryValidForController.dispose();
    _numberOfEntriesController.dispose();
    _processingTimeController.dispose();
    _applicationReferenceController.dispose();
    super.dispose();
  }
  
  /// Initialize the form with existing document data if available
  void _initializeForm() {
    if (widget.document == null) return;
    
    final document = widget.document!;
    _nameController.text = document.name;
    _documentNumberController.text = document.documentNumber;
    _issuedByController.text = document.issuedBy;
    _issuedDate = document.issuedDate;
    _expiryDate = document.expiryDate;
    _notesController.text = document.notes ?? '';
    
    if (document is Passport) {
      _nationalityController.text = document.nationality;
      _countryCodeController.text = document.countryCode;
      _placeOfBirthController.text = document.placeOfBirth;
      _dateOfBirth = document.dateOfBirth;
      _gender = document.gender;
      _mrzLine1Controller.text = document.mrzLine1 ?? '';
      _mrzLine2Controller.text = document.mrzLine2 ?? '';
    } else if (document is Visa) {
      _visaType = document.visaType;
      _entryType = document.entryType;
      _countryOfIssueController.text = document.countryOfIssue;
      _countryValidForController.text = document.countryValidFor;
      _maxStayDuration = document.maxStayDuration;
      _numberOfEntriesController.text = document.numberOfEntries?.toString() ?? '';
      _processingTimeController.text = document.processingTime?.toString() ?? '';
      _applicationReferenceController.text = document.applicationReference ?? '';
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCommonFields(),
          const SizedBox(height: 24),
          if (widget.documentType == TravelDocumentType.passport)
            _buildPassportFields()
          else if (widget.documentType == TravelDocumentType.visa)
            _buildVisaFields(),
          const SizedBox(height: 24),
          _buildDocumentImages(),
          const SizedBox(height: 24),
          _buildSubmitButton(),
        ],
      ),
    );
  }
  
  /// Build the common fields for all document types
  Widget _buildCommonFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Document Information',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _nameController,
          label: 'Document Name',
          hint: 'e.g., US Passport, Japan Tourist Visa',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a name';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _documentNumberController,
          label: 'Document Number',
          hint: 'e.g., *********',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter a document number';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _issuedByController,
          label: 'Issued By',
          hint: 'e.g., Department of State, Embassy of Japan',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter the issuing authority';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: DatePickerField(
                label: 'Issue Date',
                selectedDate: _issuedDate,
                onDateSelected: (date) {
                  setState(() {
                    _issuedDate = date;
                  });
                },
                validator: (date) {
                  if (date == null) {
                    return 'Please select an issue date';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DatePickerField(
                label: 'Expiry Date',
                selectedDate: _expiryDate,
                onDateSelected: (date) {
                  setState(() {
                    _expiryDate = date;
                  });
                },
                validator: (date) {
                  if (date == null) {
                    return 'Please select an expiry date';
                  }
                  if (_issuedDate != null && date.isBefore(_issuedDate!)) {
                    return 'Expiry date must be after issue date';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _notesController,
          label: 'Notes (Optional)',
          hint: 'Any additional information about this document',
          maxLines: 3,
        ),
      ],
    );
  }
  
  /// Build the passport-specific fields
  Widget _buildPassportFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Passport Information',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _nationalityController,
                label: 'Nationality',
                hint: 'e.g., United States',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter nationality';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _countryCodeController,
                label: 'Country Code',
                hint: 'e.g., US',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter country code';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _placeOfBirthController,
          label: 'Place of Birth',
          hint: 'e.g., New York, NY',
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter place of birth';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: DatePickerField(
                label: 'Date of Birth',
                selectedDate: _dateOfBirth,
                onDateSelected: (date) {
                  setState(() {
                    _dateOfBirth = date;
                  });
                },
                validator: (date) {
                  if (date == null) {
                    return 'Please select date of birth';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String>(
                value: _gender,
                decoration: const InputDecoration(
                  labelText: 'Gender',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'M', child: Text('Male')),
                  DropdownMenuItem(value: 'F', child: Text('Female')),
                  DropdownMenuItem(value: 'X', child: Text('Other')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _gender = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _mrzLine1Controller,
          label: 'MRZ Line 1 (Optional)',
          hint: 'Machine Readable Zone line 1',
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _mrzLine2Controller,
          label: 'MRZ Line 2 (Optional)',
          hint: 'Machine Readable Zone line 2',
        ),
      ],
    );
  }
  
  /// Build the visa-specific fields
  Widget _buildVisaFields() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Visa Information',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<VisaType>(
                value: _visaType,
                decoration: const InputDecoration(
                  labelText: 'Visa Type',
                  border: OutlineInputBorder(),
                ),
                items: VisaType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _visaType = value;
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<VisaEntryType>(
                value: _entryType,
                decoration: const InputDecoration(
                  labelText: 'Entry Type',
                  border: OutlineInputBorder(),
                ),
                items: VisaEntryType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _entryType = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _countryOfIssueController,
                label: 'Country of Issue',
                hint: 'e.g., United States',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter country of issue';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _countryValidForController,
                label: 'Country Valid For',
                hint: 'e.g., Japan',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter country valid for';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        TextFormField(
          initialValue: _maxStayDuration.toString(),
          decoration: const InputDecoration(
            labelText: 'Maximum Stay Duration (days)',
            border: OutlineInputBorder(),
          ),
          keyboardType: TextInputType.number,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter maximum stay duration';
            }
            if (int.tryParse(value) == null || int.parse(value) <= 0) {
              return 'Please enter a valid number of days';
            }
            return null;
          },
          onChanged: (value) {
            if (int.tryParse(value) != null) {
              _maxStayDuration = int.parse(value);
            }
          },
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomTextField(
                controller: _numberOfEntriesController,
                label: 'Number of Entries (Optional)',
                hint: 'e.g., 1, 2, or multiple',
                keyboardType: TextInputType.number,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: CustomTextField(
                controller: _processingTimeController,
                label: 'Processing Time (days, Optional)',
                hint: 'e.g., 5',
                keyboardType: TextInputType.number,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        CustomTextField(
          controller: _applicationReferenceController,
          label: 'Application Reference (Optional)',
          hint: 'e.g., ABC123456',
        ),
      ],
    );
  }
  
  /// Build the document images section
  Widget _buildDocumentImages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Document Images',
          style: AppTextStyles.headline6,
        ),
        const SizedBox(height: 8),
        Text(
          'Upload images of your document (front, back, etc.)',
          style: AppTextStyles.body2.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 16),
        if (_images.isEmpty)
          Center(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.border),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.cloud_upload,
                    size: 48,
                    color: AppColors.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No images selected',
                    style: AppTextStyles.body1.copyWith(
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _images.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: FileImage(_images[index]),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _images.removeAt(index);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: AppColors.error,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton.icon(
              onPressed: _pickImage,
              icon: const Icon(Icons.photo_library),
              label: const Text('Select Image'),
            ),
            const SizedBox(width: 16),
            ElevatedButton.icon(
              onPressed: _takePhoto,
              icon: const Icon(Icons.camera_alt),
              label: const Text('Take Photo'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.secondary,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  /// Build the submit button
  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _submitForm,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: Text(
          widget.document == null ? 'Add Document' : 'Update Document',
          style: AppTextStyles.button,
        ),
      ),
    );
  }
  
  /// Pick an image from the gallery
  Future<void> _pickImage() async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
    );
    
    if (pickedFile != null) {
      setState(() {
        _images.add(File(pickedFile.path));
      });
    }
  }
  
  /// Take a photo with the camera
  Future<void> _takePhoto() async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.camera,
      imageQuality: 80,
    );
    
    if (pickedFile != null) {
      setState(() {
        _images.add(File(pickedFile.path));
      });
    }
  }
  
  /// Submit the form
  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      if (_images.isEmpty && widget.document == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please add at least one image of your document'),
            backgroundColor: AppColors.error,
          ),
        );
        return;
      }
      
      final now = DateTime.now();
      TravelDocument document;
      
      if (widget.documentType == TravelDocumentType.passport) {
        document = Passport(
          id: widget.document?.id ?? '',
          userId: widget.document?.userId ?? '',
          name: _nameController.text,
          documentNumber: _documentNumberController.text,
          issuedBy: _issuedByController.text,
          issuedDate: _issuedDate!,
          expiryDate: _expiryDate!,
          status: widget.document?.status ?? TravelDocumentStatus.valid,
          notes: _notesController.text.isEmpty ? null : _notesController.text,
          documentImageUrls: widget.document?.documentImageUrls ?? [],
          createdAt: widget.document?.createdAt ?? now,
          updatedAt: now,
          nationality: _nationalityController.text,
          countryCode: _countryCodeController.text,
          placeOfBirth: _placeOfBirthController.text,
          dateOfBirth: _dateOfBirth!,
          gender: _gender,
          mrzLine1: _mrzLine1Controller.text.isEmpty ? null : _mrzLine1Controller.text,
          mrzLine2: _mrzLine2Controller.text.isEmpty ? null : _mrzLine2Controller.text,
        );
      } else {
        document = Visa(
          id: widget.document?.id ?? '',
          userId: widget.document?.userId ?? '',
          name: _nameController.text,
          documentNumber: _documentNumberController.text,
          issuedBy: _issuedByController.text,
          issuedDate: _issuedDate!,
          expiryDate: _expiryDate!,
          status: widget.document?.status ?? TravelDocumentStatus.valid,
          notes: _notesController.text.isEmpty ? null : _notesController.text,
          documentImageUrls: widget.document?.documentImageUrls ?? [],
          createdAt: widget.document?.createdAt ?? now,
          updatedAt: now,
          visaType: _visaType,
          entryType: _entryType,
          countryOfIssue: _countryOfIssueController.text,
          countryValidFor: _countryValidForController.text,
          maxStayDuration: _maxStayDuration,
          numberOfEntries: _numberOfEntriesController.text.isEmpty
              ? null
              : int.tryParse(_numberOfEntriesController.text),
          processingTime: _processingTimeController.text.isEmpty
              ? null
              : int.tryParse(_processingTimeController.text),
          applicationReference: _applicationReferenceController.text.isEmpty
              ? null
              : _applicationReferenceController.text,
        );
      }
      
      widget.onSubmit(document, _images);
    }
  }
}
