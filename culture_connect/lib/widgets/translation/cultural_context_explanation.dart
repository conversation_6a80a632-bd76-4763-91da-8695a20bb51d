import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:culture_connect/models/translation/cultural_context_model.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/utils/rtl_utils.dart';

/// A widget that displays an explanation for a cultural context note
class CulturalContextExplanation extends StatelessWidget {
  /// The cultural context note to explain
  final CulturalContextNote note;

  /// Whether to animate the widget
  final bool animate;

  /// Creates a new cultural context explanation
  const CulturalContextExplanation({
    super.key,
    required this.note,
    this.animate = true,
  });

  @override
  Widget build(BuildContext context) {
    // Determine text direction based on the context
    final textDirection = Directionality.of(context);
    final isRTL = textDirection == TextDirection.rtl;
    final textAlign = isRTL ? TextAlign.right : TextAlign.left;

    final explanation = Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: note.type.color.withAlpha(13),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: note.type.color.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment:
            isRTL ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              // Type badge
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: note.type.color.withAlpha(25),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      note.type.icon,
                      size: 16.r,
                      color: note.type.color,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      note.type.displayName,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: note.type.color,
                      ),
                    ),
                  ],
                ),
              ),

              // Region badge (if available)
              if (note.region != null) ...[
                SizedBox(width: 8.w),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    note.region!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.grey[700],
                    ),
                  ),
                ),
              ],

              // Formality level badge (if available)
              if (note.formalityLevel != null) ...[
                SizedBox(width: 8.w),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color:
                        _getFormalityColor(note.formalityLevel!).withAlpha(25),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Text(
                    note.formalityLevel!,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: _getFormalityColor(note.formalityLevel!),
                    ),
                  ),
                ),
              ],
            ],
          ),

          SizedBox(height: 12.h),

          // Text segment
          if (note.textSegment.isNotEmpty) ...[
            Text(
              'Original Text:',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: textAlign,
            ),
            SizedBox(height: 4.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(8.r),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(
                  color: Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: Text(
                '"${note.textSegment}"',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontStyle: FontStyle.italic,
                  color: Colors.black87,
                ),
                textAlign: textAlign,
              ),
            ),
            SizedBox(height: 12.h),
          ],

          // Explanation
          Text(
            'Explanation:',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
            textAlign: textAlign,
          ),
          SizedBox(height: 4.h),
          Text(
            note.explanation,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.black87,
            ),
            textAlign: textAlign,
          ),

          // Alternatives (if available)
          if (note.alternatives != null && note.alternatives!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            Text(
              'Alternative Expressions:',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: textAlign,
            ),
            SizedBox(height: 8.h),
            Wrap(
              spacing: 8.w,
              runSpacing: 8.h,
              children: note.alternatives!.map((alternative) {
                return Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: AppTheme.primaryColor.withAlpha(76),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    alternative,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],

          // Resources (if available)
          if (note.resources != null && note.resources!.isNotEmpty) ...[
            SizedBox(height: 16.h),
            Text(
              'Learn More:',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
              textAlign: textAlign,
            ),
            SizedBox(height: 8.h),
            ...note.resources!.map((resource) {
              return Padding(
                padding: EdgeInsets.only(bottom: 4.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.link,
                      size: 16.r,
                      color: AppTheme.primaryColor,
                    ),
                    SizedBox(width: 8.w),
                    Expanded(
                      child: Text(
                        resource,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppTheme.primaryColor,
                          decoration: TextDecoration.underline,
                        ),
                        textAlign: textAlign,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ],
      ),
    );

    if (animate) {
      return explanation.animate().fadeIn(duration: 300.ms).slideY(
          begin: 0.1, end: 0, duration: 300.ms, curve: Curves.easeOutQuad);
    }

    return explanation;
  }

  /// Get the color for a formality level
  Color _getFormalityColor(String formalityLevel) {
    switch (formalityLevel.toLowerCase()) {
      case 'formal':
      case 'very formal':
        return Colors.indigo;
      case 'informal':
      case 'very informal':
        return Colors.orange;
      case 'neutral':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }
}
