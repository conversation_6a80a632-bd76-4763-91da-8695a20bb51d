import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/pronunciation_model.dart';
import 'package:culture_connect/models/translation/translation_pronunciation.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for displaying a pronunciation indicator
class PronunciationIndicator extends StatelessWidget {
  /// The pronunciation information
  final TranslationPronunciation pronunciation;
  
  /// Whether to show the label
  final bool showLabel;
  
  /// Whether to use a compact layout
  final bool compact;
  
  /// Whether to use light colors (for dark backgrounds)
  final bool useLight;
  
  /// Callback when the indicator is tapped
  final VoidCallback? onTap;
  
  /// Creates a new pronunciation indicator
  const PronunciationIndicator({
    super.key,
    required this.pronunciation,
    this.showLabel = true,
    this.compact = false,
    this.useLight = false,
    this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    if (!pronunciation.hasPronunciationGuides) {
      return const SizedBox.shrink();
    }
    
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          Icon(
            Icons.record_voice_over,
            size: compact ? 14.r : 18.r,
            color: useLight ? Colors.white70 : Colors.teal,
          ),
          
          if (showLabel) ...[
            SizedBox(width: 4.w),
            
            // Label
            Text(
              compact ? 'Pronunciation' : 'Pronunciation Guide',
              style: TextStyle(
                fontSize: compact ? 10.sp : 12.sp,
                color: useLight
                    ? Colors.white70
                    : AppTheme.textSecondaryColor,
              ),
            ),
          ],
          
          // Badge with count
          if (pronunciation.guideCount > 0) ...[
            SizedBox(width: 4.w),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              decoration: BoxDecoration(
                color: useLight
                    ? Colors.white24
                    : Colors.teal.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                pronunciation.guideCount.toString(),
                style: TextStyle(
                  fontSize: compact ? 8.sp : 10.sp,
                  fontWeight: FontWeight.bold,
                  color: useLight
                      ? Colors.white
                      : Colors.teal,
                ),
              ),
            ),
          ],
          
          // Difficult badge
          if (pronunciation.hasDifficultGuides) ...[
            SizedBox(width: 4.w),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              decoration: BoxDecoration(
                color: useLight
                    ? Colors.white24
                    : Colors.orange.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                Icons.priority_high,
                size: compact ? 10.r : 12.r,
                color: useLight ? Colors.white : Colors.orange,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A widget for displaying a pronunciation guide
class PronunciationGuideCard extends StatelessWidget {
  /// The pronunciation guide
  final PronunciationGuide guide;
  
  /// Callback when the audio button is tapped
  final Function(String)? onPlayAudio;
  
  /// Creates a new pronunciation guide card
  const PronunciationGuideCard({
    super.key,
    required this.guide,
    this.onPlayAudio,
  });
  
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.only(bottom: 12.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
        side: BorderSide(
          color: guide.difficulty.color.withOpacity(0.3),
          width: 1.w,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(12.r),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.r),
                  decoration: BoxDecoration(
                    color: guide.difficulty.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    guide.difficulty.icon,
                    size: 20.r,
                    color: guide.difficulty.color,
                  ),
                ),
                SizedBox(width: 8.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      guide.text,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      '${guide.difficulty.displayName} Difficulty',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: guide.difficulty.color,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                if (guide.audioPath != null)
                  IconButton(
                    icon: const Icon(Icons.volume_up),
                    color: Colors.teal,
                    onPressed: () {
                      if (onPlayAudio != null) {
                        onPlayAudio!(guide.audioPath!);
                      }
                    },
                  ),
              ],
            ),
            
            SizedBox(height: 16.h),
            
            // Pronunciation
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        guide.type.icon,
                        size: 16.r,
                        color: Colors.teal,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        '${guide.type.displayName} Pronunciation',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.teal,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    guide.pronunciation,
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimaryColor,
                      fontFamily: guide.type == PronunciationGuideType.ipa
                          ? 'NotoSans'
                          : null,
                    ),
                  ),
                ],
              ),
            ),
            
            // Tips (if available)
            if (guide.tips != null && guide.tips!.isNotEmpty) ...[
              SizedBox(height: 16.h),
              Text(
                'Pronunciation Tips',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(height: 8.h),
              ...guide.tips!.map((tip) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 4.h),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.tips_and_updates_outlined,
                        size: 16.r,
                        color: Colors.amber,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          tip,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ],
            
            // Common mistakes (if available)
            if (guide.commonMistakes != null && guide.commonMistakes!.isNotEmpty) ...[
              SizedBox(height: 16.h),
              Text(
                'Common Mistakes',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(height: 8.h),
              ...guide.commonMistakes!.map((mistake) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 4.h),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 16.r,
                        color: Colors.red,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Text(
                          mistake,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ],
            
            // Additional information
            if (guide.region != null) ...[
              SizedBox(height: 12.h),
              Row(
                children: [
                  Icon(
                    Icons.location_on_outlined,
                    size: 16.r,
                    color: Colors.green,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    '${guide.region} dialect',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
