import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/translation_feedback_model.dart';
import 'package:culture_connect/providers/translation_feedback_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget for providing translation feedback
class TranslationFeedbackForm extends ConsumerStatefulWidget {
  /// The message that was translated
  final MessageModel message;
  
  /// The translation metadata
  final MessageTranslationMetadata translationMetadata;
  
  /// Callback when feedback is submitted
  final VoidCallback? onFeedbackSubmitted;
  
  /// Creates a new translation feedback form
  const TranslationFeedbackForm({
    super.key,
    required this.message,
    required this.translationMetadata,
    this.onFeedbackSubmitted,
  });
  
  @override
  ConsumerState<TranslationFeedbackForm> createState() => _TranslationFeedbackFormState();
}

class _TranslationFeedbackFormState extends ConsumerState<TranslationFeedbackForm> {
  final TextEditingController _correctionController = TextEditingController();
  final TextEditingController _commentsController = TextEditingController();
  
  TranslationQuality _selectedQuality = TranslationQuality.fair;
  TranslationFeedbackType _selectedFeedbackType = TranslationFeedbackType.general;
  
  bool _isSubmitting = false;
  
  @override
  void dispose() {
    _correctionController.dispose();
    _commentsController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Translation Feedback',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          // Original text
          _buildTextSection(
            'Original Text (${widget.translationMetadata.sourceLanguage})',
            widget.translationMetadata.originalText,
          ),
          
          SizedBox(height: 16.h),
          
          // Translated text
          _buildTextSection(
            'Translated Text (${widget.translationMetadata.targetLanguage})',
            widget.translationMetadata.translatedText,
          ),
          
          SizedBox(height: 24.h),
          
          // Quality rating
          Text(
            'How would you rate this translation?',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          _buildQualitySelector(),
          
          SizedBox(height: 24.h),
          
          // Feedback type
          Text(
            'What type of feedback are you providing?',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          _buildFeedbackTypeSelector(),
          
          SizedBox(height: 24.h),
          
          // Suggested correction
          Text(
            'Suggested Correction (Optional)',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          TextField(
            controller: _correctionController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Enter your suggested translation...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              contentPadding: EdgeInsets.all(12.r),
            ),
          ),
          
          SizedBox(height: 24.h),
          
          // Additional comments
          Text(
            'Additional Comments (Optional)',
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          
          SizedBox(height: 8.h),
          
          TextField(
            controller: _commentsController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Enter any additional comments...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              contentPadding: EdgeInsets.all(12.r),
            ),
          ),
          
          SizedBox(height: 24.h),
          
          // Submit button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _isSubmitting ? null : _submitFeedback,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 12.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.r),
                ),
              ),
              child: _isSubmitting
                  ? SizedBox(
                      height: 20.h,
                      width: 20.w,
                      child: const CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : Text(
                      'Submit Feedback',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build a text section
  Widget _buildTextSection(String title, String text) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        
        SizedBox(height: 4.h),
        
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(12.r),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1.w,
            ),
          ),
          child: Text(
            text,
            style: TextStyle(
              fontSize: 16.sp,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ],
    );
  }
  
  /// Build the quality selector
  Widget _buildQualitySelector() {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: TranslationQuality.values.map((quality) {
        final isSelected = quality == _selectedQuality;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedQuality = quality;
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: isSelected ? quality.color.withOpacity(0.2) : Colors.grey[100],
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: isSelected ? quality.color : Colors.grey[300]!,
                width: 1.w,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  quality.icon,
                  size: 16.r,
                  color: isSelected ? quality.color : Colors.grey,
                ),
                SizedBox(width: 4.w),
                Text(
                  quality.displayName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: isSelected ? quality.color : Colors.grey[700],
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
  
  /// Build the feedback type selector
  Widget _buildFeedbackTypeSelector() {
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: TranslationFeedbackType.values.map((type) {
        final isSelected = type == _selectedFeedbackType;
        
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedFeedbackType = type;
            });
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: isSelected ? AppTheme.primaryColor.withOpacity(0.1) : Colors.grey[100],
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : Colors.grey[300]!,
                width: 1.w,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  type.icon,
                  size: 16.r,
                  color: isSelected ? AppTheme.primaryColor : Colors.grey,
                ),
                SizedBox(width: 4.w),
                Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: isSelected ? AppTheme.primaryColor : Colors.grey[700],
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
  
  /// Submit feedback
  Future<void> _submitFeedback() async {
    if (_isSubmitting) return;
    
    setState(() {
      _isSubmitting = true;
    });
    
    try {
      final feedback = await ref.read(translationFeedbackNotifierProvider.notifier).submitFeedback(
        messageId: widget.message.id,
        sourceLanguage: widget.translationMetadata.sourceLanguage,
        targetLanguage: widget.translationMetadata.targetLanguage,
        originalText: widget.translationMetadata.originalText,
        translatedText: widget.translationMetadata.translatedText,
        suggestedCorrection: _correctionController.text.isNotEmpty
            ? _correctionController.text
            : null,
        quality: _selectedQuality,
        feedbackType: _selectedFeedbackType,
        comments: _commentsController.text.isNotEmpty
            ? _commentsController.text
            : null,
        originalConfidence: widget.translationMetadata.confidence?.overallScore,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Thank you for your feedback!'),
            backgroundColor: AppTheme.primaryColor,
            duration: const Duration(seconds: 2),
          ),
        );
        
        Navigator.pop(context);
        
        if (widget.onFeedbackSubmitted != null) {
          widget.onFeedbackSubmitted!();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to submit feedback: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}
