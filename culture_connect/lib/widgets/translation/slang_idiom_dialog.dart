import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/translation/slang_idiom_model.dart';
import 'package:culture_connect/models/translation/translation_slang_idiom.dart';
import 'package:culture_connect/providers/slang_idiom_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/translation/slang_idiom_indicator.dart';

/// A dialog for displaying slang and idiom information
class SlangIdiomDialog extends ConsumerStatefulWidget {
  /// The slang and idiom information
  final TranslationSlangIdiom slangIdiom;
  
  /// Creates a new slang and idiom dialog
  const SlangIdiomDialog({
    super.key,
    required this.slangIdiom,
  });
  
  @override
  ConsumerState<SlangIdiomDialog> createState() => _SlangIdiomDialogState();
}

class _SlangIdiomDialogState extends ConsumerState<SlangIdiomDialog> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ExpressionType? _selectedType;
  FormalityLevel? _selectedFormalityLevel;
  bool _showPotentiallyOffensiveContent = false;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _showPotentiallyOffensiveContent = ref.read(showPotentiallyOffensiveContentProvider);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: 500.w,
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: EdgeInsets.all(16.r),
              decoration: BoxDecoration(
                color: Colors.purple,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  topRight: Radius.circular(16.r),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.format_quote,
                    size: 24.r,
                    color: Colors.white,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Slang & Idiom Expressions',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            
            // Tab bar
            TabBar(
              controller: _tabController,
              labelColor: Colors.purple,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.purple,
              tabs: const [
                Tab(text: 'Expressions'),
                Tab(text: 'About'),
              ],
            ),
            
            // Tab content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  // Expressions tab
                  _buildExpressionsTab(),
                  
                  // About tab
                  _buildAboutTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build the expressions tab
  Widget _buildExpressionsTab() {
    // Filter expressions based on selected type, formality level, and offensive content
    final expressions = widget.slangIdiom.expressions.where((expression) {
      if (_selectedType != null && expression.type != _selectedType) {
        return false;
      }
      if (_selectedFormalityLevel != null && expression.formalityLevel != _selectedFormalityLevel) {
        return false;
      }
      if (expression.isPotentiallyOffensive && !_showPotentiallyOffensiveContent) {
        return false;
      }
      return true;
    }).toList();
    
    return Column(
      children: [
        // Filter bar
        Padding(
          padding: EdgeInsets.all(12.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Type filter
              Text(
                'Filter by Type:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 8.h),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // All types option
                    _buildTypeFilterChip(null, 'All'),
                    SizedBox(width: 8.w),
                    
                    // Type options
                    ...ExpressionType.values.map((type) {
                      // Only show types that have expressions
                      if (widget.slangIdiom.getExpressionsOfType(type).isEmpty) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: EdgeInsets.only(right: 8.w),
                        child: _buildTypeFilterChip(type, type.displayName),
                      );
                    }).toList(),
                  ],
                ),
              ),
              
              SizedBox(height: 12.h),
              
              // Formality level filter
              Text(
                'Filter by Formality:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              SizedBox(height: 8.h),
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // All formality levels option
                    _buildFormalityFilterChip(null, 'All'),
                    SizedBox(width: 8.w),
                    
                    // Formality level options
                    ...FormalityLevel.values.map((level) {
                      // Check if there are expressions with this formality level
                      final hasExpressions = widget.slangIdiom.expressions.any(
                        (expression) => expression.formalityLevel == level,
                      );
                      if (!hasExpressions) {
                        return const SizedBox.shrink();
                      }
                      return Padding(
                        padding: EdgeInsets.only(right: 8.w),
                        child: _buildFormalityFilterChip(level, level.displayName),
                      );
                    }).toList(),
                  ],
                ),
              ),
              
              SizedBox(height: 12.h),
              
              // Potentially offensive content toggle
              Row(
                children: [
                  Text(
                    'Show Potentially Offensive Content:',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const Spacer(),
                  Switch(
                    value: _showPotentiallyOffensiveContent,
                    onChanged: (value) {
                      setState(() {
                        _showPotentiallyOffensiveContent = value;
                      });
                      ref.read(slangIdiomNotifierProvider.notifier)
                          .setShowPotentiallyOffensiveContent(value);
                    },
                    activeColor: Colors.purple,
                  ),
                ],
              ),
              
              // Divider
              Divider(height: 24.h),
            ],
          ),
        ),
        
        // Expressions list
        Expanded(
          child: expressions.isEmpty
              ? Center(
                  child: Text(
                    _getEmptyStateMessage(),
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                )
              : ListView.builder(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  itemCount: expressions.length,
                  itemBuilder: (context, index) {
                    return SlangIdiomExpressionCard(expression: expressions[index]);
                  },
                ),
        ),
      ],
    );
  }
  
  /// Build the about tab
  Widget _buildAboutTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Language pair information
          Text(
            'Language Information',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          _buildLanguagePairInfo(),
          
          SizedBox(height: 16.h),
          
          // General notes
          if (widget.slangIdiom.generalNotes != null) ...[
            Text(
              'General Notes',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            SizedBox(height: 8.h),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(12.r),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: Colors.purple.withOpacity(0.3),
                  width: 1.w,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 20.r,
                        color: Colors.purple,
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        'About Slang & Idioms',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textPrimaryColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    widget.slangIdiom.generalNotes!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          SizedBox(height: 16.h),
          
          // Expression type information
          Text(
            'Types of Expressions',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          ...ExpressionType.values.map((type) {
            return _buildExpressionTypeInfo(type);
          }).toList(),
          
          SizedBox(height: 16.h),
          
          // Formality levels
          Text(
            'Formality Levels',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          SizedBox(height: 8.h),
          ...FormalityLevel.values.map((level) {
            return _buildFormalityLevelInfo(level);
          }).toList(),
        ],
      ),
    );
  }
  
  /// Build a type filter chip
  Widget _buildTypeFilterChip(ExpressionType? type, String label) {
    final isSelected = _selectedType == type;
    final color = type?.color ?? Colors.grey;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = isSelected ? null : type;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
          vertical: 6.h,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1.w,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (type != null) ...[
              Icon(
                type.icon,
                size: 16.r,
                color: isSelected ? color : Colors.grey,
              ),
              SizedBox(width: 4.w),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                color: isSelected ? color : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// Build a formality level filter chip
  Widget _buildFormalityFilterChip(FormalityLevel? level, String label) {
    final isSelected = _selectedFormalityLevel == level;
    final color = level?.color ?? Colors.grey;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFormalityLevel = isSelected ? null : level;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 12.w,
          vertical: 6.h,
        ),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : Colors.grey[100],
          borderRadius: BorderRadius.circular(16.r),
          border: Border.all(
            color: isSelected ? color : Colors.grey[300]!,
            width: 1.w,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14.sp,
            color: isSelected ? color : Colors.grey[700],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
  
  /// Build language pair information
  Widget _buildLanguagePairInfo() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                _getLanguageName(widget.slangIdiom.sourceLanguage),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              SizedBox(width: 8.w),
              Icon(
                Icons.arrow_forward,
                size: 16.r,
                color: Colors.grey,
              ),
              SizedBox(width: 8.w),
              Text(
                _getLanguageName(widget.slangIdiom.targetLanguage),
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
            ],
          ),
          if (widget.slangIdiom.sourceRegion != null ||
              widget.slangIdiom.targetRegion != null) ...[
            SizedBox(height: 8.h),
            Row(
              children: [
                if (widget.slangIdiom.sourceRegion != null)
                  Text(
                    '${widget.slangIdiom.sourceRegion} dialect',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                if (widget.slangIdiom.sourceRegion != null &&
                    widget.slangIdiom.targetRegion != null) ...[
                  SizedBox(width: 8.w),
                  Icon(
                    Icons.arrow_forward,
                    size: 14.r,
                    color: Colors.grey,
                  ),
                  SizedBox(width: 8.w),
                ],
                if (widget.slangIdiom.targetRegion != null)
                  Text(
                    '${widget.slangIdiom.targetRegion} dialect',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
          ],
          SizedBox(height: 8.h),
          Text(
            'Total expressions: ${widget.slangIdiom.expressionCount}',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          if (widget.slangIdiom.slangCount > 0 || widget.slangIdiom.idiomCount > 0) ...[
            SizedBox(height: 4.h),
            Text(
              'Slang: ${widget.slangIdiom.slangCount}, Idioms: ${widget.slangIdiom.idiomCount}',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  /// Build expression type information
  Widget _buildExpressionTypeInfo(ExpressionType type) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            type.icon,
            size: 20.r,
            color: type.color,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  type.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  _getExpressionTypeDescription(type),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Build formality level information
  Widget _buildFormalityLevelInfo(FormalityLevel level) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.all(4.r),
            decoration: BoxDecoration(
              color: level.color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.person_outline,
              size: 16.r,
              color: level.color,
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  level.displayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  _getFormalityLevelDescription(level),
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  /// Get the language name from a language code
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'fr':
        return 'French';
      case 'es':
        return 'Spanish';
      case 'yo':
        return 'Yoruba';
      case 'ig':
        return 'Igbo';
      case 'ha':
        return 'Hausa';
      case 'sw':
        return 'Swahili';
      default:
        return code;
    }
  }
  
  /// Get the description for an expression type
  String _getExpressionTypeDescription(ExpressionType type) {
    switch (type) {
      case ExpressionType.slang:
        return 'Informal language used by particular groups, often changing rapidly.';
      case ExpressionType.idiom:
        return 'Expressions that cannot be understood from the individual meanings of their words.';
      case ExpressionType.colloquialism:
        return 'Informal expressions used in everyday conversation rather than formal speech or writing.';
      case ExpressionType.proverb:
        return 'Short, well-known sayings that express a truth or give advice.';
      case ExpressionType.metaphor:
        return 'Expressions that describe something by referring to something else with similar qualities.';
      case ExpressionType.euphemism:
        return 'Mild or indirect expressions used in place of ones considered harsh or blunt.';
      case ExpressionType.jargon:
        return 'Special words or expressions used by a profession or group that are difficult for others to understand.';
    }
  }
  
  /// Get the description for a formality level
  String _getFormalityLevelDescription(FormalityLevel level) {
    switch (level) {
      case FormalityLevel.veryInformal:
        return 'Used among close friends, family, or in very casual settings. May be inappropriate in formal contexts.';
      case FormalityLevel.informal:
        return 'Used in casual, everyday conversations with friends, family, and peers.';
      case FormalityLevel.neutral:
        return 'Appropriate for most everyday situations, neither particularly formal nor informal.';
      case FormalityLevel.formal:
        return 'Used in professional settings, with people you don\'t know well, or in official communications.';
      case FormalityLevel.veryFormal:
        return 'Used in highly formal situations, official documents, or ceremonial contexts.';
    }
  }
  
  /// Get the empty state message based on the current filters
  String _getEmptyStateMessage() {
    if (_selectedType != null && _selectedFormalityLevel != null) {
      return 'No ${_selectedType!.displayName.toLowerCase()} expressions with ${_selectedFormalityLevel!.displayName.toLowerCase()} formality level found';
    } else if (_selectedType != null) {
      return 'No ${_selectedType!.displayName.toLowerCase()} expressions found';
    } else if (_selectedFormalityLevel != null) {
      return 'No expressions with ${_selectedFormalityLevel!.displayName.toLowerCase()} formality level found';
    } else if (!_showPotentiallyOffensiveContent && widget.slangIdiom.hasPotentiallyOffensiveContent) {
      return 'Some expressions are hidden because they may be offensive. Enable "Show Potentially Offensive Content" to view them.';
    } else {
      return 'No expressions found';
    }
  }
}
