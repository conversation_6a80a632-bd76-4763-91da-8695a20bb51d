import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/loyalty/loyalty_program_model.dart';
import 'package:culture_connect/providers/loyalty_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// A widget that displays a loyalty tier card
class LoyaltyTierCard extends ConsumerWidget {
  /// The loyalty program
  final LoyaltyProgramModel loyaltyProgram;

  /// Whether to show the progress bar
  final bool showProgressBar;

  /// Whether to show the next tier
  final bool showNextTier;

  /// Whether to show the benefits
  final bool showBenefits;

  /// Callback when the card is tapped
  final VoidCallback? onTap;

  /// Creates a new loyalty tier card
  const LoyaltyTierCard({
    super.key,
    required this.loyaltyProgram,
    this.showProgressBar = true,
    this.showNextTier = true,
    this.showBenefits = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final currentTier = loyaltyProgram.tier;
    final nextTier = loyaltyProgram.nextTier;
    final progressToNextTier = loyaltyProgram.progressToNextTier;
    final pointsToNextTier = loyaltyProgram.pointsToNextTier;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16.r),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                currentTier.color.withOpacity(0.8),
                currentTier.color.withOpacity(0.4),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Tier badge and points
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Tier badge
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.5),
                        width: 1.w,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          currentTier.icon,
                          size: 16.r,
                          color: Colors.white,
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          currentTier.displayName,
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Points
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${loyaltyProgram.pointsBalance}',
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        'Points',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              SizedBox(height: 16.h),

              // Program name
              Text(
                loyaltyProgram.name,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),

              SizedBox(height: 4.h),

              // Member since
              Text(
                'Member since ${loyaltyProgram.enrollmentDate.year}',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Colors.white.withOpacity(0.8),
                ),
              ),

              // Progress to next tier
              if (showProgressBar &&
                  nextTier != null &&
                  progressToNextTier != null &&
                  pointsToNextTier != null) ...[
                SizedBox(height: 16.h),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Progress to ${nextTier.displayName}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                    Text(
                      '$pointsToNextTier points to go',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 8.h),

                // Progress bar
                ClipRRect(
                  borderRadius: BorderRadius.circular(4.r),
                  child: LinearProgressIndicator(
                    value: progressToNextTier,
                    backgroundColor: Colors.white.withOpacity(0.2),
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    minHeight: 8.h,
                  ),
                ),
              ],

              // Next tier info
              if (showNextTier && nextTier != null) ...[
                SizedBox(height: 16.h),
                Container(
                  padding: EdgeInsets.all(12.r),
                  decoration: BoxDecoration(
                    color: Colors.white
                        .withAlpha(26), // 0.1 * 255 = 25.5, rounded to 26
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.arrow_upward,
                        size: 20.r,
                        color: Colors.white,
                      ),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Next Tier: ${nextTier.displayName}',
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            SizedBox(height: 4.h),
                            Text(
                              'Earn ${pointsToNextTier ?? 0} more points to unlock ${nextTier.discountPercentage}% discount and more benefits!',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.white.withOpacity(0.8),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Benefits
              if (showBenefits) ...[
                SizedBox(height: 16.h),
                Text(
                  'Your Benefits',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: 8.h),
                ...currentTier.benefits
                    .map((benefit) => Padding(
                          padding: EdgeInsets.only(bottom: 4.h),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.check_circle,
                                size: 16.r,
                                color: Colors.white,
                              ),
                              SizedBox(width: 8.w),
                              Expanded(
                                child: Text(
                                  benefit,
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    color: Colors.white.withOpacity(0.8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ))
                    .toList(),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
