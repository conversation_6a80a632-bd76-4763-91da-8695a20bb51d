import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:culture_connect/models/payment_method.dart';
import 'package:culture_connect/services/payment_service.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/payment/credit_card_form.dart';

/// A bottom sheet for adding a new payment method
class AddPaymentMethodSheet extends ConsumerStatefulWidget {
  /// Callback when a payment method is added
  final Function(PaymentMethod) onPaymentMethodAdded;

  /// Creates a new add payment method sheet
  const AddPaymentMethodSheet({
    super.key,
    required this.onPaymentMethodAdded,
  });

  @override
  ConsumerState<AddPaymentMethodSheet> createState() =>
      _AddPaymentMethodSheetState();
}

class _AddPaymentMethodSheetState extends ConsumerState<AddPaymentMethodSheet> {
  PaymentMethodType _selectedType = PaymentMethodType.creditCard;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(16.r),
        ),
      ),
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(24.r),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Add Payment Method',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              SizedBox(height: 24.h),

              // Payment method type selection
              Text(
                'Select Payment Method Type',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 16.h),
              _buildPaymentMethodTypeSelector(),
              SizedBox(height: 24.h),

              // Payment method form
              _buildPaymentMethodForm(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentMethodTypeSelector() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildPaymentTypeOption(
              PaymentMethodType.creditCard, Icons.credit_card),
          _buildPaymentTypeOption(PaymentMethodType.paypal, Icons.payment),
          _buildPaymentTypeOption(PaymentMethodType.applePay, Icons.apple),
          _buildPaymentTypeOption(
              PaymentMethodType.googlePay, Icons.g_mobiledata),
          _buildPaymentTypeOption(
              PaymentMethodType.mobileMoney, Icons.phone_android),
          _buildPaymentTypeOption(
              PaymentMethodType.bankAccount, Icons.account_balance),
        ],
      ),
    );
  }

  Widget _buildPaymentTypeOption(PaymentMethodType type, IconData icon) {
    final isSelected = _selectedType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = type;
        });
      },
      child: Container(
        width: 80.w,
        margin: EdgeInsets.only(right: 12.w),
        padding: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : Colors.grey.shade300,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : Colors.grey,
              size: 24.sp,
            ),
            SizedBox(height: 8.h),
            Text(
              type.displayName,
              style: TextStyle(
                color: isSelected ? Colors.white : Colors.black,
                fontSize: 12.sp,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodForm() {
    switch (_selectedType) {
      case PaymentMethodType.creditCard:
        return CreditCardForm(
          onSubmit: _addCreditCard,
          isLoading: _isLoading,
        );
      case PaymentMethodType.paypal:
        return _buildDigitalWalletForm('PayPal');
      case PaymentMethodType.applePay:
        return _buildDigitalWalletForm('Apple Pay');
      case PaymentMethodType.googlePay:
        return _buildDigitalWalletForm('Google Pay');
      case PaymentMethodType.mobileMoney:
        return _buildMobileMoneyForm();
      case PaymentMethodType.bankAccount:
        return _buildBankAccountForm();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildDigitalWalletForm(String walletName) {
    // Placeholder for digital wallet form
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 24.h),
        child: Column(
          children: [
            Icon(
              Icons.account_balance_wallet,
              size: 48.sp,
              color: Colors.grey,
            ),
            SizedBox(height: 16.h),
            Text(
              '$walletName integration coming soon',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'This payment method will be available in a future update.',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileMoneyForm() {
    // Placeholder for mobile money form
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 24.h),
        child: Column(
          children: [
            Icon(
              Icons.phone_android,
              size: 48.sp,
              color: Colors.grey,
            ),
            SizedBox(height: 16.h),
            Text(
              'Mobile Money integration coming soon',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'This payment method will be available in a future update.',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBankAccountForm() {
    // Placeholder for bank account form
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 24.h),
        child: Column(
          children: [
            Icon(
              Icons.account_balance,
              size: 48.sp,
              color: Colors.grey,
            ),
            SizedBox(height: 16.h),
            Text(
              'Bank Account integration coming soon',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'This payment method will be available in a future update.',
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addCreditCard({
    required String cardNumber,
    required String cardHolderName,
    required int expiryMonth,
    required int expiryYear,
    required String cvv,
    required bool setAsDefault,
  }) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final paymentService = PaymentService();

      // Format the card number for display
      final formattedCardNumber = cardNumber.replaceAll(' ', '');
      final last4 =
          formattedCardNumber.substring(formattedCardNumber.length - 4);
      final brand = _getCardBrand(formattedCardNumber);

      // Add the payment method
      final paymentMethod = await paymentService.addPaymentMethod(
        type: PaymentMethodType.creditCard,
        name: '$brand ending in $last4',
        last4: last4,
        brand: brand,
        expiryMonth: expiryMonth,
        expiryYear: expiryYear,
        isDefault: setAsDefault,
      );

      // Call the callback
      widget.onPaymentMethodAdded(paymentMethod);

      // Close the sheet
      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error adding payment method: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _getCardBrand(String cardNumber) {
    // Basic card brand detection
    if (cardNumber.startsWith('4')) {
      return 'Visa';
    } else if (cardNumber.startsWith(RegExp(r'5[1-5]'))) {
      return 'Mastercard';
    } else if (cardNumber.startsWith(RegExp(r'3[47]'))) {
      return 'American Express';
    } else if (cardNumber.startsWith(RegExp(r'6(?:011|5[0-9]{2})'))) {
      return 'Discover';
    }
    return 'Card';
  }
}
