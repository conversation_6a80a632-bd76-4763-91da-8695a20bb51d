# CultureConnect Issues Fix Tracker

This document tracks the linting errors and issues in the CultureConnect project, along with their fixes.

## Project Structure Guidelines
- All code must be placed within the `culture_connect/lib` folder following the single source of truth principle
- Package imports (`package:culture_connect/...`) must be used rather than relative imports
- Code should follow the structure defined in the project architecture

## Issues by Category

### 1. Import Issues

#### Fixed
- **[2023-05-15]** Fixed missing import for `TranslationDatabaseHelper` and `BandwidthSettings` in `bandwidth_provider.dart`
  - Added imports for required classes
  - Solution: Added `import 'package:culture_connect/database/translation_database_helper.dart'` and created the missing `BandwidthSettings` class

- **[2023-05-15]** Fixed import issues in `location_service.dart`
  - Added proper imports for custom `LatLng` class
  - Solution: Used import alias `import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;` to avoid conflicts

- **[2023-05-15]** Fixed ambiguous import for `BandwidthSettings` in `bandwidth_provider.dart`
  - Solution: Removed the import for `bandwidth_settings.dart` since `BandwidthSettings` is already defined in `bandwidth_usage.dart`

#### Pending
- Unused imports in various files (e.g., `router.dart`, `main.dart`, etc.)
- Missing imports in various files
- Ambiguous imports (e.g., `BookingStatus` defined in multiple files)
- Relative imports that should be package imports

### 2. Missing Files/Classes

#### Fixed
- **[2023-05-15]** Created missing `BandwidthSettings` class
  - Solution: Created new file at `culture_connect/lib/models/offline/bandwidth_settings.dart`

- **[2023-05-15]** Created missing `LatLng` class
  - Solution: Created new file at `culture_connect/lib/models/location/lat_lng.dart`

- **[2023-05-15]** Created missing `PlaceResult` class
  - Solution: Created new file at `culture_connect/lib/models/location/place_result.dart`

- **[2023-05-15]** Created missing `GeoLocation` class
  - Solution: Created new file at `culture_connect/lib/models/location/geo_location.dart`

- **[2023-05-15]** Created missing `PassengerInfo` class
  - Solution: Created new file at `culture_connect/lib/models/travel/flight/passenger_info.dart`

- **[2023-05-15]** Created missing `BookingInfo` class
  - Solution: Created new file at `culture_connect/lib/models/travel/flight/booking_info.dart`

- **[2023-05-15]** Created missing `Flight` class
  - Solution: Created new file at `culture_connect/lib/models/travel/flight/flight.dart`

- **[2023-05-15]** Created missing `AdditionalService` class
  - Solution: Created new file at `culture_connect/lib/models/travel/flight/additional_service.dart`

- **[2023-05-15]** Created missing `BookingSummary` class
  - Solution: Created new file at `culture_connect/lib/models/travel/flight/booking_summary.dart`

- **[2023-05-15]** Created missing `FareBreakdown` class
  - Solution: Created new file at `culture_connect/lib/models/travel/flight/fare_breakdown.dart`

- **[2023-05-15]** Created missing `ChatModel` class
  - Solution: Created new file at `culture_connect/lib/models/chat/chat_model.dart`

- **[2023-05-15]** Created missing `StorageService` class
  - Solution: Created new file at `culture_connect/lib/services/storage_service.dart`

- **[2023-05-15]** Created missing `FileUtils` class
  - Solution: Created new file at `culture_connect/lib/utils/file_utils.dart`

#### Pending
- No more missing classes

### 3. Type Errors

#### Fixed
- **[2023-05-15]** Fixed `LatLng` type errors in `transfer_location.dart`
  - Solution: Created custom `LatLng` class and updated imports

- **[2023-05-15]** Fixed `LatLng` constructor to use positional parameters
  - Solution: Updated constructor and `copyWith` method to use positional parameters

- **[2023-05-15]** Fixed `gmaps.LatLng` vs custom `LatLng` conflicts in `location_service.dart`
  - Solution: Used import alias and updated references to use the correct type

- **[2023-05-15]** Fixed null return issue in `restaurant_provider.dart`
  - Solution: Used try-catch instead of `orElse: () => null` to handle the case when no restaurant is found

- **[2023-05-15]** Updated `TravelService` to use `GeoLocation` instead of `LatLng`
  - Solution: Changed the type of the coordinates field in the TravelService class

- **[2023-05-15]** Updated travel service models to use `GeoLocation` instead of `LatLng`
  - Solution: Updated TransferService, CarRental, and PrivateSecurity classes to use GeoLocation

#### Pending
- Return type mismatches (e.g., `Future<List<Booking>>` vs `Future<List<BookingModel>>`)
- Parameter type mismatches
- Null safety issues (e.g., properties accessed without null checks)
- Performance issues (e.g., missing `const` constructors)

### 4. Method/Function Issues

#### Fixed
- **[2023-05-15]** Added missing methods to `location_service.dart`
  - Solution: Implemented `searchPlaces`, `searchNearbyPlaces`, and `getNearbyAirports` methods

- **[2023-05-15]** Fixed math functions in `transfer_location_service.dart`
  - Solution: Implemented proper math helper functions for distance calculations

#### Pending
- Missing methods in various other services
- Override annotations on methods that don't override anything
- Methods that don't call required super methods

### 5. Dependency Issues

#### Fixed
- **[2023-05-19]** Fixed project structure issue with root-level "lib" directory
  - Solution: Moved all files from root-level "lib" to "culture_connect/lib" and updated imports

#### Pending
- Duplicate `connectivity_plus` dependency in `pubspec.yaml`

### 6. UI/Widget Issues

#### Pending
- Deprecated method usage (e.g., `withOpacity`, `onPopInvoked`)
- Missing required parameters in widget constructors

## Progress Summary

### Fixed Issues Count: 35
- BandwidthSettings class created
- LatLng class created
- PlaceResult class created
- GeoLocation class created
- PassengerInfo class created
- BookingInfo class created
- Flight class created
- AdditionalService class created
- BookingSummary class created
- FareBreakdown class created
- ChatModel class created
- StorageService class created
- FileUtils class created
- Import issues in bandwidth_provider.dart fixed
- Import issues in location_service.dart fixed
- Ambiguous import for BandwidthSettings fixed
- LatLng type errors fixed
- Math functions in transfer_location_service.dart fixed
- Missing methods in location_service.dart added
- Added missing methods to booking_service.dart
- Fixed null return issue in restaurant_provider.dart
- Updated TravelService to use GeoLocation instead of LatLng
- Updated TransferService to use GeoLocation instead of LatLng
- Updated CarRental to use GeoLocation instead of LatLng
- Updated PrivateSecurity to use GeoLocation instead of LatLng
- Fixed project structure issue with root-level "lib" directory
- Created Reader type definition for provider access
- Verified ContentConflictResolution enum implementation
- Verified SyncStatus enum implementation
- Verified PassengerInfo class implementation
- Verified BookingInfo class implementation
- Verified Flight class implementation
- Added missing dependencies to pubspec.yaml (record, just_audio, pdf, mime, table_calendar)
- Fixed duplicate connectivity_plus dependency in pubspec.yaml
- Fixed LatLng type conflicts in transfer_location_provider.dart

### Pending Issues Count: ~14453
- Most issues are related to missing classes, type errors, and import problems
- Many issues are in test files that need to be updated
- Performance issues (e.g., missing `const` constructors)
- Many issues are related to missing files and dependencies

## Next Steps
1. Create missing files and classes referenced in imports
2. Fix type errors in providers (especially in booking_provider.dart and flight_providers.dart)
3. Address missing methods in services (especially in document_service.dart)
4. Fix dependency issues (duplicate connectivity_plus in pubspec.yaml, add missing dependencies)
5. Address UI/widget issues (deprecated methods like withOpacity, onPopInvoked)
6. Fix test files (missing imports, undefined classes, etc.)

## Current Analysis Results
Running `flutter analyze` shows approximately 14453 issues, including:
- Multiple errors about undefined classes and missing files
- Missing dependencies in pubspec.yaml (e.g., record, just_audio, pdf, table_calendar, stripe_payment)
- Type errors in various providers
- Missing methods in services
- Deprecated method usage in UI components (e.g., withOpacity, onPopInvoked)
- Test files with missing dependencies and undefined classes
- Syntax errors in some files (e.g., unterminated string literals in slang_idiom_service.dart)

## Notes
- Many issues are interconnected - fixing one may resolve others
- Some issues may require refactoring of multiple files
- Priority is given to structural issues that affect multiple files

## Missing Dependencies
The following dependencies need to be added to pubspec.yaml:
- record: For voice recording functionality
- just_audio: For audio playback in voice translation
- pdf: For receipt generation and document handling
- table_calendar: For calendar views in booking screens
- stripe_payment: For payment processing
- flutterwave_standard: For alternative payment processing

## Syntax Errors
- slang_idiom_service.dart: Contains unterminated string literals in examples array
- voice_translation_service.dart: Contains duplicate method declarations (_speechToText, _translateText)
- logging_service.dart: Contains duplicate declaration of _logToCrashlytics

## Summary of Progress
We have successfully fixed the project structure issue by:
1. Moving all files from the root-level "lib" directory to "culture_connect/lib"
2. Creating a symbolic link from root-level "lib" to "culture_connect/lib" to maintain compatibility
3. Updated imports to use package imports (package:culture_connect/...)
4. Identified additional issues that need to be addressed

## High Priority Issues
The following issues should be addressed next:
1. Missing classes referenced in imports:
   - Reader class used in providers (cultural_context_provider.dart, message_translation_provider.dart, etc.)
   - ContentConflictResolution class in enhanced_offline_mode_provider.dart
   - SyncStatus class in database/translation_database_helper.dart
   - GroupTranslationModel and related classes

2. Missing dependencies in pubspec.yaml:
   - record: For voice recording functionality
   - just_audio: For audio playback
   - pdf: For receipt generation
   - mime: For file operations in file_utils.dart
   - table_calendar: For calendar views

3. Type errors in providers:
   - PassengerInfo and BookingInfo in flight_providers.dart
   - LatLng type conflicts in transfer_location_provider.dart
